---
type: "always_apply"
---

# Unibest UniApp 微信小程序开发规范

> 基于 unibest 3.4.0 脚手架的核心开发规范
>
> **技术栈**: Vue3 + TypeScript + Vite5 + UnoCSS + wot-design-uni + Pinia

---

## 1. 核心规范

### 1.1 命名规范
- **页面文件**: kebab-case，如 `user-profile.vue`
- **组件文件**: PascalCase，如 `UserCard.vue`
- **变量函数**: camelCase，如 `userName`, `getUserInfo()`
- **常量**: UPPER_SNAKE_CASE，如 `MAX_RETRY_COUNT`

### 1.2 目录结构
```
src/
├── components/     # 公共组件
├── pages/         # 主包页面
├── pages-sub/     # 分包页面
├── api/           # API 接口
├── store/         # Pinia 状态管理
├── utils/         # 工具函数
├── types/         # TypeScript 类型
└── static/        # 静态资源
```

### 1.3 重要配置规则

#### ⚠️ 禁止直接修改自动生成的文件
- `src/pages.json` - 由 `pages.config.ts` 自动生成
- `src/manifest.json` - 由 `manifest.config.ts` 自动生成

#### 正确配置方式
**页面配置**: 在 Vue 文件中使用 `<route>` 块
```vue
<route lang="json">
{
  "type": "home",
  "style": {
    "navigationBarTitleText": "首页"
  }
}
</route>
```

**全局配置**: 使用 `pages.config.ts` 和 `manifest.config.ts`

### 1.4 Vue 组件规范
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入依赖
// 2. 定义 props 和 emits
// 3. 响应式数据和计算属性
// 4. 方法和生命周期
</script>

<style lang="scss" scoped>
/* 样式内容 */
</style>
```

## 2. TypeScript 规范
- 所有 API 接口必须定义类型
- 组件 props 使用 TypeScript 接口
- 避免使用 `any`，使用 `unknown` 替代
- 优先使用 `interface` 而不是 `type`

## 3. 样式规范
- 使用 SCSS + UnoCSS
- 组件样式必须使用 `scoped`
- 使用 `rpx` 单位适配不同屏幕

## 4. API 规范

### 4.1 自动生成接口（推荐）
项目集成了 `openapi-ts-request` 插件，支持根据接口文档自动生成代码。

**配置步骤：**
1. 在 `openapi-ts-request.config.ts` 中配置接口文档 URL
2. 执行 `pnpm run openapi-ts-request` 生成代码
3. 在业务代码中使用生成的接口

**使用示例：**
```typescript
// GET 请求
const { data, isLoading } = useQuery(
  getUserInfoQueryOptions({ params: { id: userId } })
)

// POST 请求
const { mutate } = useCreateUserMutation({
  onSuccess: () => console.log('创建成功')
})
```

### 4.2 手动接口定义
```typescript
// api/custom.ts
export const customApi = {
  getData: (params: any) => http.get('/api/data', params),
  postData: (data: any) => http.post('/api/data', data),
}
```

## 5. 状态管理规范（Pinia）
```typescript
// stores/user.ts
export const useUserStore = defineStore('user', () => {
  const userInfo = ref<UserInfo | null>(null)
  const isLogin = computed(() => !!userInfo.value)

  const login = async (params: LoginParams) => {
    const res = await userApi.login(params)
    userInfo.value = res.data
  }

  return { userInfo, isLogin, login }
})
```

## 6. 性能优化规范
- 使用 WebP 格式图片
- 图片懒加载
- 路由懒加载
- 组件按需导入



## 7. 环境配置
- 开发环境: `.env.development`
- 生产环境: `.env.production`
- 使用 `VITE_` 前缀暴露给前端

## 8. 安全规范
- 敏感数据加密存储
- 用户输入数据验证
- Token 认证和权限控制

## 9. 微信小程序规范

### 9.1 生命周期管理
```typescript
// 页面生命周期
import { onLoad, onShow, onReady } from '@dcloudio/uni-app'

onLoad((options) => {
  // 页面加载，获取参数
  console.log('页面参数:', options)
})

onShow(() => {
  // 页面显示，刷新数据
})

onReady(() => {
  // 页面渲染完成
})
```

### 9.2 小程序 API 使用
```typescript
// 权限申请
export async function requestPermission(scope: string) {
  const { authSetting } = await uni.getSetting()
  if (!authSetting[scope]) {
    await uni.authorize({ scope })
  }
}

// 存储管理
export const storage = {
  set: (key: string, value: any) => uni.setStorageSync(key, value),
  get: (key: string) => uni.getStorageSync(key),
  remove: (key: string) => uni.removeStorageSync(key)
}
```

## 10. 组件开发规范

### 10.1 组件设计原则
- 单一职责：一个组件只做一件事
- 可复用：通过 props 配置不同状态
- 可测试：逻辑清晰，便于测试

### 10.2 组件通信
```typescript
// 父子组件通信
interface Props {
  user: UserInfo
  showEmail?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showEmail: true
})

const emit = defineEmits<{
  click: [user: UserInfo]
}>()
```

## 11. 缓存策略
```typescript
// Vue Query 缓存配置
const { data } = useQuery({
  ...getUserInfoQueryOptions({ userId }),
  staleTime: 5 * 60 * 1000,  // 5分钟内数据不过期
  cacheTime: 10 * 60 * 1000, // 缓存保持10分钟
})
```

## 12. 错误处理
```typescript
// 统一错误处理
export const errorHandler = {
  handle(error: any) {
    console.error('错误:', error)

    // 用户友好提示
    uni.showToast({
      title: error.message || '操作失败，请稍后重试',
      icon: 'none'
    })
  }
}

// 使用示例
try {
  await api.getData()
} catch (error) {
  errorHandler.handle(error)
}
```

## 13. 常见问题

### 13.1 配置文件问题
- **问题**: 修改 `pages.json`、`manifest.json` 被覆盖
- **解决**: 使用 `pages.config.ts` 和 `manifest.config.ts` 配置

### 13.2 开发环境问题
- **首次运行报错**: 先执行 `pnpm i` 生成配置文件
- **代码格式报错**: 执行 `pnpm run lint:fix` 修复

### 13.3 版本要求
- **Node.js**: >= 18，推荐 22+
- **pnpm**: >= 7.30，推荐 10+

### 13.4 环境变量
```typescript
// ❌ 错误用法
const apiUrl = process.env.VITE_API_URL

// ✅ 正确用法
const apiUrl = import.meta.env.VITE_API_URL
```

---

## 📋 快速开始

### 常用命令
```bash
# 安装依赖
pnpm i

# 生成接口代码（如果有配置）
pnpm run openapi-ts-request

# 开发环境
pnpm run dev:h5          # H5 开发
pnpm run dev:mp-weixin   # 微信小程序开发

# 代码检查和格式化
pnpm run lint            # 检查代码
pnpm run lint:fix        # 修复代码格式

# 构建
pnpm run build:h5        # H5 构建
pnpm run build:mp-weixin # 微信小程序构建
```

### 环境要求
- Node.js >= 18
- pnpm >= 7.30
- 微信开发者工具

### 重要提醒
- 新项目首先执行 `pnpm i` 生成配置文件
- 接口文档更新后执行 `pnpm run openapi-ts-request`
- 禁止直接修改 `src/pages.json` 和 `src/manifest.json`

---

**请严格遵守以上规范，确保项目代码质量和团队协作效率。**