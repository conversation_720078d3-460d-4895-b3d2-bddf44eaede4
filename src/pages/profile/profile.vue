<route lang="jsonc">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "我的",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useUserStore } from '@/store/user'
import { getSafeAreaStyle } from '@/utils/safeArea'
import { useToast } from 'wot-design-uni'

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 获取用户状态管理
const userStore = useUserStore()

// 获取 toast 组件
const toast = useToast()

// 定义加载状态变量
const isNavigating = ref(false)

// 分享面板相关状态
const showSharePanel = ref<boolean>(false)
const sharePanels = ref([
  {
    iconUrl: '//img12.360buyimg.com/imagetools/jfs/t1/122016/33/6657/1362/5f0692a1E8708d245/e47299e5945a6956.png',
    title: '微信好友'
  },
  {
    iconUrl: 'https://img14.360buyimg.com/imagetools/jfs/t1/111572/11/11734/1245/5f0692a1E39d13d21/b35dfe9243bd6c2a.png',
    title: '微信朋友圈'
  },
  {
    iconUrl: 'https://img14.360buyimg.com/imagetools/jfs/t1/132639/25/4003/945/5f069336E18778248/fa181913030bed8a.png',
    title: 'QQ好友'
  },
  {
    iconUrl: 'https://img14.360buyimg.com/imagetools/jfs/t1/134807/4/3950/1256/5f069336E76949e27/d20641da8e699f07.png',
    title: '微信收藏'
  }
])

// 显示微型加载动画并导航（带错误处理）
function navigateWithAnimation(url: string) {
  isNavigating.value = true

  setTimeout(() => {
    isNavigating.value = false
    uni.navigateTo({
      url,
      animationType: 'slide-in-right',
      animationDuration: 300,
      success: () => {
        console.log('跳转成功:', url)
      },
      fail: (err) => {
        console.error('跳转失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
        })
      },
    })
  }, 300)
}

// 帮助与反馈（分包路径）
function navigateToHelp() {
  navigateWithAnimation('/pages-sub/help/help')
}

// 处理分享 - 暴露给页面
// #ifdef MP-WEIXIN
function onShareAppMessage() {
  return {
    title: '趣水印 - 一键去除短视频水印',
    path: '/pages/index/index',
    imageUrl: '/static/images/ppxlogo.png',
  }
}

// 同样支持分享到朋友圈
function onShareTimeline() {
  return {
    title: '趣水印 - 一键去除短视频水印',
    path: '/pages/index/index',
    imageUrl: '/static/images/ppxlogo.png',
  }
}

defineExpose({
  onShareAppMessage,
  onShareTimeline,
})
// #endif

// 显示分享面板
function showShareActions() {
  showSharePanel.value = true
}

// 关闭分享面板
function closeSharePanel() {
  showSharePanel.value = false
}

// 处理分享选择
function handleShareSelect({ item, index }: { item: any, index: number }) {
  toast.show(`选择了: ${item.title}`)
  closeSharePanel()

  // 这里可以根据不同的分享类型执行不同的分享逻辑
  switch (item.title) {
    case '微信好友':
      // 触发微信好友分享
      console.log('分享到微信好友')
      break
    case '微信朋友圈':
      // 触发朋友圈分享
      console.log('分享到微信朋友圈')
      break
    case 'QQ好友':
      // 触发QQ好友分享
      console.log('分享到QQ好友')
      break
    case '微信收藏':
      // 触发微信收藏
      console.log('收藏到微信')
      break
  }
}

// 页面加载时确保分享功能已启用
onMounted(() => {
  // #ifdef MP-WEIXIN
  if (wx.showShareMenu) {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline'],
    })
  }
  // #endif
})
</script>

<template>
  <view class="mx-auto bg-gray-50 px-4 flex flex-col" :style="{ ...safeAreaStyle, minHeight: '100vh' }">
    <!-- 微型加载动画 -->
    <view v-if="isNavigating" class="mini-loading" />

    <!-- 主要内容区域 -->
    <view class="flex-1">
      <!-- 用户资料卡片 -->
      <view class="mb-6 rounded-2xl bg-white p-5 shadow-sm">
        <view class="flex items-center">
          <view class="mr-4 h-16 w-16 flex items-center justify-center overflow-hidden rounded-full bg-transparent">
            <!-- 使用logo图片作为头像 -->
            <image src="/static/images/ppxlogo.png" mode="aspectFit" class="h-full w-full" />
          </view>
          <view class="flex-1">
            <text class="mb-1 text-lg text-gray-800 font-medium">
              趣水印
            </text>
            <text class="text-sm text-gray-500">
              Version: 1.0.0
            </text>
          </view>
        </view>
      </view>

      <!-- 其他设置菜单 -->
      <view class="mb-6 rounded-2xl bg-white py-1 shadow-sm">
        <view class="menu-item flex items-center px-5 py-4 active:bg-gray-50" @click="navigateToHelp">
          <view class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-gray-100">
            <text class="i-carbon-help text-gray-500" />
          </view>
          <text class="text-gray-800 font-medium">
            帮助与反馈
          </text>
          <text class="i-carbon-chevron-right ml-auto text-gray-400" />
        </view>

        <!-- 分享应用 -->
        <view class="menu-item flex items-center px-5 py-4 active:bg-gray-50" @click="showShareActions">
          <view class="mr-3 h-8 w-8 flex items-center justify-center rounded-full bg-gray-100">
            <text class="i-carbon-share text-gray-500" />
          </view>
          <text class="text-gray-800 font-medium">
            分享应用
          </text>
          <text class="i-carbon-chevron-right ml-auto text-gray-400" />
        </view>
      </view>
    </view>

    <!-- 应用信息 - 固定在底部 -->
    <view class="mt-auto pt-8 pb-6 text-center">
      <text class="text-xs text-gray-400">
        趣水印 v1.0.0
      </text>
      <text class="mt-1 block text-xs text-gray-400">
        © 2025 迷失
      </text>
    </view>

    <!-- 分享面板 -->
    <wd-action-sheet
      v-model="showSharePanel"
      :panels="sharePanels"
      safe-area-inset-bottom
      @close="closeSharePanel"
      @select="handleShareSelect"
    />

    <!-- Toast 组件 -->
    <wd-toast />
  </view>
</template>

<style scoped>
/* 添加微型加载指示器的样式 */
.mini-loading {
  width: 24px;
  height: 24px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  z-index: 9999;
}

@keyframes spin {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.menu-item {
  position: relative;
}

.menu-item::after {
  content: '';
  position: absolute;
  left: 16px;
  right: 16px;
  bottom: 0;
  height: 1px;
  background-color: #f3f4f6;
}

.menu-item:last-child::after {
  display: none;
}

/* 分享面板样式优化 */
:deep(.wd-action-sheet__panel) {
  display: flex !important;
  justify-content: space-around !important;
  align-items: center !important;
  padding: 20px 16px !important;
}

:deep(.wd-action-sheet__panel-item) {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 0 !important;
}

:deep(.wd-action-sheet__panel-item-icon) {
  width: 48px !important;
  height: 48px !important;
  margin-bottom: 8px !important;
  border-radius: 12px !important;
}

:deep(.wd-action-sheet__panel-item-title) {
  font-size: 12px !important;
  color: #666 !important;
  text-align: center !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

</style>
