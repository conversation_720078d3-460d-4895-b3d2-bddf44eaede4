<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "趣水印",
    "navigationBarBackgroundColor": "#FFFFFF",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { parseVipVideo } from '@/api/vipvideo'
import { parseDouyin } from '@/api/douyin'
import { getSafeAreaStyle } from '@/utils/safeArea'

defineOptions({
  name: 'Home',
})

// 获取安全区域样式
const safeAreaStyle = getSafeAreaStyle()

// 响应式数据
const linkInput = ref('')
const isLoading = ref(false)

// 监听输入变化，只允许输入有效的URL格式
watch(linkInput, (newValue) => {
  if (!newValue) return

  // 使用严格的URL正则匹配，只保留符合URL格式的内容
  const urlPattern = /[a-zA-Z]+:\/\/[^\s]*/g
  const matches = newValue.match(urlPattern)

  if (matches && matches.length > 0) {
    // 如果匹配到URL，只保留第一个完整的URL
    const validUrl = matches[0]
    if (validUrl !== newValue) {
      linkInput.value = validUrl
    }
  } else {
    // 如果没有匹配到完整URL，但输入内容包含非ASCII字符（如中文），清空输入框
    if (/[^\x00-\x7F]/.test(newValue)) {
      linkInput.value = ''
    }
    // 如果是正在输入URL的过程中，允许保留（如输入"http"等）
    else if (!/^[a-zA-Z]*:?\/?\/?\w*/.test(newValue)) {
      linkInput.value = ''
    }
  }
}, { immediate: true })

// 支持的平台 - 使用确实存在的Carbon图标
const supportedPlatforms = [
  { icon: 'i-carbon-music', bgColor: 'bg-black' }, 
  { icon: 'i-carbon-fire', bgColor: 'bg-red-500' }, 
  { icon: 'i-carbon-book', bgColor: 'bg-pink-500' }, 
  { icon: 'i-carbon-share', bgColor: 'bg-blue-500' }, 
  { icon: 'i-carbon-document', bgColor: 'bg-red-600' },
  { icon: 'i-carbon-video', bgColor: 'bg-green-500' }, 
  { icon: 'i-carbon-play', bgColor: 'bg-purple-500' }, 
  { icon: 'i-carbon-menu', bgColor: 'bg-gray-400' }, 
]

// 提取文本中的URL，使用通用URL匹配
function extractUrl(text: string): string | null {
  // 通用URL匹配模式
  const urlPattern = /https?:\/\/[^\s\u4E00-\u9FFF，。！？；："'（）【】《》]+/gi

  const matches = text.match(urlPattern)
  if (matches && matches.length > 0) {
    // 清理链接，移除末尾可能的标点符号
    const url = matches[0].replace(/[，。！？；："'（）【】《》\s]*$/, '')
    return url
  }

  return null
}



// 从剪贴板粘贴
function pasteFromClipboard() {
  uni.getClipboardData({
    success: (res) => {
      if (res.data && res.data.trim()) {
        const clipboardText = res.data.trim()

        // 尝试提取纯净的URL链接
        const extractedUrl = extractUrl(clipboardText)

        if (extractedUrl) {
          // 只在输入框显示提取出的纯净链接，过滤掉所有中文内容
          linkInput.value = extractedUrl
          uni.showToast({
            title: '链接提取成功',
            icon: 'success',
            duration: 1500,
          })
        }
        else {
          // 如果没有找到有效链接，不填充输入框，提示用户
          uni.showToast({
            title: '未找到有效链接，请检查内容',
            icon: 'none',
            duration: 2000,
          })
        }
      }
      else {
        uni.showToast({
          title: '剪贴板为空',
          icon: 'none',
          duration: 1500,
        })
      }
    },
    fail: () => {
      uni.showToast({
        title: '获取剪贴板失败',
        icon: 'none',
        duration: 1500,
      })
    },
  })
}

// 处理解析
async function handleExtract() {
  if (!linkInput.value.trim()) {
    uni.showToast({
      title: '请输入链接',
      icon: 'none',
    })
    return
  }

  const url = linkInput.value.trim()
  isLoading.value = true

  try {
    let result: any

    // 判断是否为抖音链接
    if (url.includes('douyin.com')) {
      // 调用抖音专用接口
      const douyinData = await parseDouyin({ url })

      // 检查API返回数据是否有效
      if (!douyinData) {
        throw new Error('解析失败，未获取到有效数据')
      }

      // 直接使用API返回的数据，只添加必要的字段
      result = {
        ...douyinData,
        type: douyinData.video_url ? 'video' : 'img',
        originalUrl: url,
      }
    } else {
      // 调用通用VIP视频解析接口
      const vipVideoData = await parseVipVideo({ url })

      // 检查API返回数据是否有效
      if (!vipVideoData) {
        throw new Error('解析失败，未获取到有效数据')
      }

      // 转换数据格式以兼容现有逻辑
      result = {
        title: vipVideoData.title,
        cover: vipVideoData.cover,
        type: vipVideoData.type === 'video' ? 'video' : 'img',
        video_url: vipVideoData.video_url,
        images: vipVideoData.images,
        count: vipVideoData.count,
        originalUrl: url,
      }

      // 只在有值时添加可选字段
      if (vipVideoData.author) {
        result.author = vipVideoData.author
      }
      if (vipVideoData.like) {
        result.like = vipVideoData.like
      }
    }

    // 跳转到结果页面（分包路径）
    const resultUrl = result.type === 'video'
      ? `/pages-sub/result/result?data=${encodeURIComponent(JSON.stringify(result))}`
      : `/pages-sub/result-album/result-album?data=${encodeURIComponent(JSON.stringify(result))}`

    uni.navigateTo({
      url: resultUrl,
      success: () => {
        console.log('跳转成功')
      },
      fail: (err) => {
        console.error('跳转失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
        })
      },
    })

    // 清空输入框
    linkInput.value = ''
  }
  catch (error) {
    console.error('解析失败:', error)
    // HTTP拦截器已经处理了错误提示，这里不需要额外处理
  }
  finally {
    isLoading.value = false
  }
}

// 清空输入框内容
function clearInput() {
  linkInput.value = ''
  uni.showToast({
    title: '已清空',
    icon: 'success',
    duration: 1000,
  })
}

// 页面加载时初始化
onMounted(() => {
  // 初始化代码（如有需要）
})
</script>

<template>
  <view class="mx-auto min-h-screen bg-gray-50 px-4" :style="safeAreaStyle">
    <!-- 链接输入区域 -->
    <wd-card title="粘贴链接获取内容">
      <view class="relative">
        <textarea
          v-model="linkInput" placeholder="请粘贴视频或图集分享链接..."
          class="min-h-24 w-full resize-none border border-gray-200 rounded-xl bg-gray-50 p-4 text-sm" :maxlength="500"
        />
        <view class="absolute bottom-2 right-2 flex items-center gap-2" style="z-index: 10;">
          <!-- 清空按钮 -->
          <wd-button
            v-if="linkInput.trim()"
            type="text"
            size="small"
            custom-style="pointer-events: auto;"
            @click="clearInput">
            <wd-icon name="close" size="16px" color="#999" />
          </wd-button>

          <!-- 粘贴按钮 -->
          <wd-button type="text" size="small" custom-style="pointer-events: auto;" @click="pasteFromClipboard">
            粘贴
          </wd-button>
        </view>
      </view>

      <wd-button
        type="primary" block size="large" :loading="isLoading" :disabled="!linkInput.trim()" class="mb-3 mt-4"
        @click="handleExtract"
      >
        {{ isLoading ? '正在提取中...' : '一键提取无水印内容' }}
      </wd-button>

      <text class="block text-center text-xs text-gray-500">
        支持各大平台视频及图集
      </text>
    </wd-card>

    <!-- 区域间隔 -->
    <view class="mb-8" />

    <!-- 平台支持 -->
    <wd-card title="支持平台" class="mt-4">
      <view class="platform-grid py-4">
        <view v-for="(platform, index) in supportedPlatforms" :key="index" class="platform-item">
          <view class="platform-icon" :class="[platform.bgColor]">
            <text :class="platform.icon" class="text-white" />
          </view>
        </view>
      </view>
    </wd-card>

    <!-- 底部间距 -->
    <view class="mb-16" />
  </view>
</template>

<style scoped>
.min-h-24 {
  min-height: 6rem;
}

/* 平台网格布局 */
.platform-grid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding: 32rpx 0;
}

.platform-item {
  width: 25%;
  box-sizing: border-box;
  padding: 0 16rpx;
  margin-bottom: 40rpx;
  display: flex;
  justify-content: center;
}

.platform-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.platform-icon:active {
  transform: scale(0.95);
}
</style>
